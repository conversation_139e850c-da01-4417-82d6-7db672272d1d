<?php
// Di<PERSON>lukan header untuk mengizinkan akses dari domain lain
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Sertakan file database dan model komentar
include_once '../../core/Database.php';
include_once '../../models/Comment.php';

// Inisialisasi koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek komentar
$comment = new Comment($db);

// Ambil data POST yang dikirim dalam format JSON
$data = json_decode(file_get_contents("php://input"));

// Pastikan data tidak kosong
if (
    !empty($data->recipe_id) &&
    !empty($data->user_id) &&
    !empty($data->comment_text)
) {
    // Atur nilai properti komentar
    $comment->recipe_id = $data->recipe_id;
    $comment->user_id = $data->user_id;
    $comment->comment_text = $data->comment_text;

    // Coba simpan komentar
    if ($comment->createComment()) {
        http_response_code(201); // 201 Created
        echo json_encode(array("message" => "Komentar berhasil ditambahkan."));
    } else {
        http_response_code(503); // 503 Service Unavailable
        echo json_encode(array("message" => "Tidak dapat menambahkan komentar."));
    }
} else {
    http_response_code(400); // 400 Bad Request
    echo json_encode(array("message" => "Tidak dapat menambahkan komentar. Data tidak lengkap."));
}
?>