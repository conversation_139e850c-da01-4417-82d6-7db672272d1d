<?php
// File untuk setup database dan tabel yang diperlukan
include_once 'core/Database.php';

try {
    // Koneksi ke database
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db === null) {
        die("Error: Tidak dapat terhubung ke database. Periksa konfigurasi database di core/Database.php");
    }
    
    echo "✓ Koneksi database berhasil<br>";
    
    // Cek apakah tabel recipes sudah ada
    $check_table = "SHOW TABLES LIKE 'recipes'";
    $stmt = $db->prepare($check_table);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "⚠ Tabel 'recipes' tidak ditemukan. Membuat tabel...<br>";
        
        // Buat tabel recipes
        $create_recipes_table = "
        CREATE TABLE recipes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            ingredients TEXT NOT NULL,
            steps TEXT NOT NULL,
            image VARCHAR(500) NULL,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        if ($db->exec($create_recipes_table)) {
            echo "✓ Tabel 'recipes' berhasil dibuat<br>";
        } else {
            echo "✗ Gagal membuat tabel 'recipes'<br>";
        }
    } else {
        echo "✓ Tabel 'recipes' sudah ada<br>";
    }
    
    // Cek apakah tabel users sudah ada
    $check_users_table = "SHOW TABLES LIKE 'users'";
    $stmt = $db->prepare($check_users_table);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "⚠ Tabel 'users' tidak ditemukan. Membuat tabel...<br>";
        
        // Buat tabel users
        $create_users_table = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        if ($db->exec($create_users_table)) {
            echo "✓ Tabel 'users' berhasil dibuat<br>";
        } else {
            echo "✗ Gagal membuat tabel 'users'<br>";
        }
    } else {
        echo "✓ Tabel 'users' sudah ada<br>";
    }
    
    // Cek apakah ada user untuk testing
    $check_user = "SELECT COUNT(*) as count FROM users";
    $stmt = $db->prepare($check_user);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        echo "⚠ Tidak ada user dalam database. Membuat user test...<br>";
        
        // Buat user test
        $insert_user = "INSERT INTO users (name, email, password) VALUES (?, ?, ?)";
        $stmt = $db->prepare($insert_user);
        $test_password = password_hash('password123', PASSWORD_DEFAULT);
        
        if ($stmt->execute(['Test User', '<EMAIL>', $test_password])) {
            echo "✓ User test berhasil dibuat (email: <EMAIL>, password: password123)<br>";
            echo "✓ User ID untuk testing: 1<br>";
        } else {
            echo "✗ Gagal membuat user test<br>";
        }
    } else {
        echo "✓ Database sudah memiliki " . $result['count'] . " user(s)<br>";
    }
    
    echo "<br><strong>Setup database selesai!</strong><br>";
    echo "<br>Sekarang Anda dapat:<br>";
    echo "1. Test API create recipe dengan user_id: 1<br>";
    echo "2. Gunakan file test_create_recipe.html untuk testing<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
