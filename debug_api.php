<?php
// File untuk debug API create recipe
echo "<h2>Debug API Create Recipe</h2>";

// Test 1: Cek koneksi database
echo "<h3>1. Test Koneksi Database</h3>";
try {
    include_once 'core/Database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✓ Koneksi database berhasil<br>";
    } else {
        echo "✗ Koneksi database gagal<br>";
    }
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "<br>";
}

// Test 2: Cek tabel recipes
echo "<h3>2. Test Tabel Recipes</h3>";
try {
    $stmt = $db->query("DESCRIBE recipes");
    echo "✓ Tabel recipes ada dengan struktur:<br>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "✗ Error tabel recipes: " . $e->getMessage() . "<br>";
}

// Test 3: Test manual insert
echo "<h3>3. Test Manual Insert</h3>";
try {
    include_once 'models/Recipe.php';
    $recipe = new Recipe($db);
    
    // Set data manual
    $recipe->title = "Test Recipe";
    $recipe->description = "Test Description";
    $recipe->ingredients = "Test Ingredients";
    $recipe->steps = "Test Steps";
    $recipe->image = null;
    $recipe->user_id = 1;
    
    if ($recipe->createRecipe()) {
        echo "✓ Manual insert berhasil<br>";
    } else {
        echo "✗ Manual insert gagal<br>";
    }
} catch (Exception $e) {
    echo "✗ Error manual insert: " . $e->getMessage() . "<br>";
}

// Test 4: Test dengan data JSON
echo "<h3>4. Test dengan Data JSON</h3>";
$test_data = json_encode(array(
    "title" => "Test Recipe JSON",
    "description" => "Test Description JSON",
    "ingredients" => "Test Ingredients JSON",
    "steps" => "Test Steps JSON",
    "user_id" => "1"
));

echo "Data JSON yang akan dikirim:<br>";
echo "<pre>" . $test_data . "</pre>";

// Simulasi pengiriman ke API
echo "<h3>5. Simulasi API Call</h3>";
echo "Gunakan curl command berikut untuk test:<br>";
echo "<pre>";
echo "curl -X POST http://localhost/sharing-resep/api/recipes/create.php \\\n";
echo "  -H \"Content-Type: application/json\" \\\n";
echo "  -d '" . $test_data . "'";
echo "</pre>";

echo "<br><strong>Atau gunakan form di bawah ini:</strong><br>";
?>

<form method="post" action="api/recipes/create.php" style="margin-top: 20px;">
    <h4>Test Form (akan dikirim sebagai form data, bukan JSON)</h4>
    <input type="text" name="title" value="Test Recipe Form" placeholder="Title"><br><br>
    <textarea name="description" placeholder="Description">Test Description Form</textarea><br><br>
    <textarea name="ingredients" placeholder="Ingredients">Test Ingredients Form</textarea><br><br>
    <textarea name="steps" placeholder="Steps">Test Steps Form</textarea><br><br>
    <input type="number" name="user_id" value="1" placeholder="User ID"><br><br>
    <button type="submit">Test dengan Form Data</button>
</form>

<script>
// Test dengan JavaScript fetch
function testWithFetch() {
    const data = {
        title: "Test Recipe Fetch",
        description: "Test Description Fetch",
        ingredients: "Test Ingredients Fetch",
        steps: "Test Steps Fetch",
        user_id: "1"
    };
    
    fetch('api/recipes/create.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        document.getElementById('result').innerHTML = 'Error: ' + error;
    });
}
</script>

<button onclick="testWithFetch()" style="margin-top: 20px;">Test dengan JavaScript Fetch</button>
<div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
