<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Create Recipe API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 100px; resize: vertical; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Test Create Recipe API</h1>

    <div class="info">
        <p><strong>Langkah-langkah:</strong></p>
        <ol>
            <li>Pastikan database sudah di-setup dengan menjalankan: <code>setup_database.php</code></li>
            <li>Isi form di bawah ini</li>
            <li>Klik "Create Recipe" untuk test API</li>
        </ol>
    </div>

    <form id="recipeForm">
        <div class="form-group">
            <label for="title">Title (Judul Resep):</label>
            <input type="text" id="title" name="title" value="Nasi Goreng Spesial" required>
        </div>
        
        <div class="form-group">
            <label for="description">Description (Deskripsi):</label>
            <textarea id="description" name="description" required>Nasi goreng dengan bumbu spesial yang lezat dan mudah dibuat</textarea>
        </div>
        
        <div class="form-group">
            <label for="ingredients">Ingredients (Bahan-bahan):</label>
            <textarea id="ingredients" name="ingredients" required>2 piring nasi putih
2 butir telur
3 siung bawang putih
2 buah cabai merah
Kecap manis secukupnya
Garam dan merica</textarea>
        </div>
        
        <div class="form-group">
            <label for="steps">Steps (Langkah-langkah):</label>
            <textarea id="steps" name="steps" required>1. Panaskan minyak di wajan
2. Tumis bawang putih hingga harum
3. Masukkan telur, orak-arik
4. Tambahkan nasi, aduk rata
5. Beri kecap manis, garam, dan merica
6. Aduk hingga merata dan sajikan</textarea>
        </div>
        
        <div class="form-group">
            <label for="image">Image URL (Opsional):</label>
            <input type="url" id="image" name="image" value="https://example.com/nasi-goreng.jpg">
        </div>
        
        <div class="form-group">
            <label for="user_id">User ID:</label>
            <input type="number" id="user_id" name="user_id" value="1" required>
        </div>
        
        <button type="submit">Create Recipe</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('recipeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">Sending request...</div>';
            
            // Collect form data
            const formData = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                ingredients: document.getElementById('ingredients').value,
                steps: document.getElementById('steps').value,
                image: document.getElementById('image').value || null,
                user_id: document.getElementById('user_id').value
            };
            
            // Show what data is being sent
            console.log('Sending data:', formData);
            
            try {
                const response = await fetch('api/recipes/create.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>Success!</h3>
                            <p>${responseData.message}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>Error (Status: ${response.status})</h3>
                            <p><strong>Message:</strong> ${responseData.message}</p>
                            ${responseData.missing_fields ? `<p><strong>Missing fields:</strong> ${responseData.missing_fields.join(', ')}</p>` : ''}
                            ${responseData.received_data ? `<p><strong>Received data:</strong> <pre>${JSON.stringify(responseData.received_data, null, 2)}</pre></p>` : ''}
                        </div>
                    `;
                }
                
                console.log('Response:', responseData);
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
                console.error('Error:', error);
            }
        });
    </script>
</body>
</html>
