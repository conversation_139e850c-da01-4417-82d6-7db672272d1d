<?php
// Header untuk mengizinkan akses dan format JSON
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: DELETE");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Memasukkan file database dan model resep
include_once '../../core/Database.php';
include_once '../../models/Recipe.php';

// Mendapatkan koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek resep
$recipe = new Recipe($db);

// Memeriksa apakah parameter 'id' ada di URL
if (isset($_GET['id'])) {
    // Mengatur ID resep yang akan dihapus
    $recipe->id = $_GET['id'];

    // Memanggil metode delete() pada objek resep
    if ($recipe->deleteRecipe()) {
        // Jika penghapusan berhasil
        http_response_code(200);
        echo json_encode(array("message" => "Resep berhasil dihapus."));
    } else {
        // Jika penghapusan gagal
        http_response_code(503);
        echo json_encode(array("message" => "Tidak dapat menghapus resep."));
    }
} else {
    // Jika parameter 'id' tidak ditemukan
    http_response_code(400);
    echo json_encode(array("message" => "Parameter ID tidak ditemukan."));
}
?>