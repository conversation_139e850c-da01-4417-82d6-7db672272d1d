<IfModule mod_rewrite.c>
    # Aktifkan mod rewrite
    RewriteEngine On

    # Hapus trailing slash di akhir URL
    RewriteRule ^(.*)/$ $1 [L,R=301]

    # Lindungi file .env, dll
    RewriteCond %{REQUEST_FILENAME} \.(env|json|md|txt|xml|yaml)$
    RewriteRule ^(.*)$ - [F,L]

    # Lindungi file konfigurasi di luar direktori public
    RewriteRule ^(core|vendor|api|src|tests)/ - [F,L]

    # Jika file atau folder yang diminta tidak ada, arahkan ke index.php
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>