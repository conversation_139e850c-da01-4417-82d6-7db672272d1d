<?php
// Nama File : api/recipes/read.php

// Di<PERSON>lukan header untuk mengizinkan akses dari domain lain
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

// Sertakan file database dan model resep
include_once '../../core/Database.php';
include_once '../../models/Recipe.php';

// Inisialisasi koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek resep
$recipe = new Recipe($db);

// Periksa apakah ada parameter ID di URL
// Jika ada, ambil satu resep. Jika tidak, ambil semua resep.
if (isset($_GET['id'])) {
    // Mengambil satu resep berdasarkan ID
    $recipe->id = $_GET['id'];
    $result = $recipe->readSingle();

    // Periksa apakah resep ditemukan
    if ($result) {
        // Data ditemukan, kirim respons JSON
        http_response_code(200);
        echo json_encode($result);
    } else {
        // Data tidak ditemukan, kirim pesan error
        http_response_code(404);
        echo json_encode(array("message" => "Resep tidak ditemukan."));
    }
} else {
    // Mengambil semua resep
    $stmt = $recipe->readAll();
    $num = $stmt->rowCount();

    if ($num > 0) {
        $recipes_arr = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            extract($row);
            $recipe_item = array(
                "id" => $id,
                "title" => $title,
                "description" => $description,
                "user_id" => $user_id
            );
            array_push($recipes_arr, $recipe_item);
        }
        http_response_code(200);
        echo json_encode($recipes_arr);
    } else {
        http_response_code(404);
        echo json_encode(array("message" => "Tidak ada resep yang ditemukan."));
    }
}
?>