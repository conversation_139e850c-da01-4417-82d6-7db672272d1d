<?php
// Nama File: api/comments/read.php

// <PERSON><PERSON>lukan header untuk mengizinkan akses dari domain lain
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

// Sertakan file database dan model komentar
include_once '../../core/Database.php';
include_once '../../models/Comment.php';

// Inisialisasi koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek komentar
$comment = new Comment($db);

// Periksa apakah ada parameter ID resep di URL
$recipe_id = isset($_GET['recipe_id']) ? $_GET['recipe_id'] : die();

// Atur nilai properti resep
$comment->recipe_id = $recipe_id;

// Mengambil komentar berdasarkan ID resep
$stmt = $comment->getCommentsByRecipeId();
$num = $stmt->rowCount();

if ($num > 0) {
    $comments_arr = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        extract($row);
        $comment_item = array(
            "id" => $id,
            "comment_text" => $comment_text,
            "user_name" => $user_name,
            "created_at" => $created_at
        );
        array_push($comments_arr, $comment_item);
    }
    http_response_code(200);
    echo json_encode($comments_arr);
} else {
    http_response_code(404);
    echo json_encode(array("message" => "Tidak ada komentar untuk resep ini."));
}
?>