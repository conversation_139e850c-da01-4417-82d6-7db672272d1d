<?php
class Comment {
    private $conn;
    private $table_name = "comments";

    public $id;
    public $recipe_id;
    public $user_id;
    public $comment_text;
    public $created_at;

    public function __construct($db){
        $this->conn = $db;
    }

    // Mengambil semua komentar untuk resep tertentu
    public function getCommentsByRecipeId(){
        // Menggunakan JOIN untuk mengambil nama pengguna yang membuat komentar
        $query = "SELECT c.id, c.comment_text, c.created_at, u.name AS user_name 
                  FROM " . $this->table_name . " c 
                  LEFT JOIN users u ON c.user_id = u.id 
                  WHERE c.recipe_id = ? 
                  ORDER BY c.created_at DESC";

        $stmt = $this->conn->prepare($query);

        // Bind ID resep
        $stmt->bindParam(1, $this->recipe_id);
        
        $stmt->execute();
        
        return $stmt;
    }

    // Menyimpan komentar baru
    public function createComment(){
        // Query untuk menyisipkan komentar baru
        $query = "INSERT INTO " . $this->table_name . " SET recipe_id=:recipe_id, user_id=:user_id, comment_text=:comment_text";
        
        $stmt = $this->conn->prepare($query);

        // Sanitasi data
        $this->recipe_id = htmlspecialchars(strip_tags($this->recipe_id));
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->comment_text = htmlspecialchars(strip_tags($this->comment_text));

        // Bind parameter
        $stmt->bindParam(":recipe_id", $this->recipe_id);
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":comment_text", $this->comment_text);

        if($stmt->execute()){
            return true;
        }
        
        return false;
    }
}
?>