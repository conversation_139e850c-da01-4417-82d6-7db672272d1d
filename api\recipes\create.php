<?php
// Diperlukan header untuk mengizinkan akses dan menentukan tipe konten
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Sertakan file database dan model resep
include_once '../../core/Database.php';
include_once '../../models/Recipe.php';

// Inisialisasi koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek resep
$recipe = new Recipe($db);

// Ambil data POST yang dikirim dalam format JSON
$data = json_decode(file_get_contents("php://input"));

// Debug: Log received data (remove this in production)
error_log("Received data: " . print_r($data, true));

// Pastikan data tidak kosong
if (
    !empty($data->title) &&
    !empty($data->description) &&
    !empty($data->ingredients) &&
    !empty($data->steps) &&
    !empty($data->user_id)
) {
    // Atur nilai properti resep
    $recipe->title = $data->title;
    $recipe->description = $data->description;
    $recipe->ingredients = $data->ingredients;
    $recipe->steps = $data->steps;
    $recipe->image = isset($data->image) ? $data->image : null; // Gambar opsional
    $recipe->user_id = $data->user_id;

    // Coba simpan resep
    if ($recipe->createRecipe()) {
        http_response_code(201); // 201 Created
        echo json_encode(array("message" => "Resep berhasil dibuat."));
    } else {
        http_response_code(503); // 503 Service Unavailable
        echo json_encode(array("message" => "Tidak dapat membuat resep."));
    }
} else {
    // Debug: Show which fields are missing
    $missing_fields = array();
    if (empty($data->title)) $missing_fields[] = "title";
    if (empty($data->description)) $missing_fields[] = "description";
    if (empty($data->ingredients)) $missing_fields[] = "ingredients";
    if (empty($data->steps)) $missing_fields[] = "steps";
    if (empty($data->user_id)) $missing_fields[] = "user_id";

    http_response_code(400); // 400 Bad Request
    echo json_encode(array(
        "message" => "Tidak dapat membuat resep. Data tidak lengkap.",
        "missing_fields" => $missing_fields,
        "received_data" => $data
    ));
}
?>