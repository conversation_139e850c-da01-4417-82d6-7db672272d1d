<?php
// Header untuk mengizinkan akses dan format JSON
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Periksa apakah pengguna terotentikasi di sini
// Untuk demo ini, kita asumsikan ID pengguna dikirim dari frontend sebagai bukti otentikasi.
// <PERSON>am aplikasi nyata, ini bisa berupa verifikasi token JWT atau sesi.
$user_id = isset($_GET['id']) ? $_GET['id'] : die(json_encode(array("message" => "Akses ditolak.")));

include_once '../../core/Database.php';
include_once '../../models/User.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);

$user->id = $user_id;

if ($user->getUserProfile()) {
    http_response_code(200);
    echo json_encode(array(
        "id" => $user->id,
        "name" => $user->name,
        "email" => $user->email,
        "created_at" => $user->created_at
    ));
} else {
    http_response_code(404);
    echo json_encode(array("message" => "Pengguna tidak ditemukan."));
}
?>