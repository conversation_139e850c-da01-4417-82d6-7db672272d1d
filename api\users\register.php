<?php
// Header untuk mengizinkan akses dan format JSON
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Memasukkan file database dan model user
include_once '../../core/Database.php';
include_once '../../models/User.php';

// Mendapatkan koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek user
$user = new User($db);

// Mendapatkan data POST
$data = json_decode(file_get_contents("php://input"));

// Memeriksa jika data tidak lengkap
if (empty($data->name) || empty($data->email) || empty($data->password)) {
    http_response_code(400); // Bad Request
    echo json_encode(array("message" => "Data tidak lengkap. Harap isi semua kolom."));
    exit();
}

// Mengatur properti objek user
$user->name = $data->name;
$user->email = $data->email;
$user->password = $data->password;

// Memeriksa jika email sudah terdaftar
if ($user->emailExists()) {
    http_response_code(409); // Conflict
    echo json_encode(array("message" => "Email sudah terdaftar."));
    exit();
}

// Mencoba mendaftarkan user
if ($user->registerUser()) {
    http_response_code(201); // Created
    echo json_encode(array("message" => "Pendaftaran berhasil. Silakan login."));
} else {
    http_response_code(503); // Service Unavailable
    echo json_encode(array("message" => "Tidak dapat mendaftar. Silakan coba lagi."));
}
?>