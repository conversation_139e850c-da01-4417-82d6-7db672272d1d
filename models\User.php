<?php
// Nama File: models/User.php
class User {
    // Koneksi database
    private $conn;
    private $table_name = "users";

    // Properti objek
    public $id;
    public $name;
    public $email;
    public $password;
    public $created_at;

    // Konstruktor dengan $db sebagai koneksi database
    public function __construct($db){
        $this->conn = $db;
    }

    // Mendaftarkan pengguna baru
    public function registerUser(){
        // Query untuk menyisipkan data pengguna baru
        $query = "INSERT INTO " . $this->table_name . " SET name=:name, email=:email, password=:password";
        $stmt = $this->conn->prepare($query);

        // Sanitasi data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->password = htmlspecialchars(strip_tags($this->password));

        // Enkripsi password sebelum disimpan
        $password_hash = password_hash($this->password, PASSWORD_BCRYPT);
        
        // Bind parameter
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password", $password_hash);

        if($stmt->execute()){
            return true;
        }
        return false;
    }

    // Memverifikasi kredensial login
    public function loginUser(){
        // Query untuk mencari pengguna berdasarkan email
        $query = "SELECT id, name, password FROM " . $this->table_name . " WHERE email = ? LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->email);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        // Periksa apakah pengguna ditemukan dan password cocok
        if($row && password_verify($this->password, $row['password'])){
            // Atur properti objek
            $this->id = $row['id'];
            $this->name = $row['name'];
            return true;
        }
        return false;
    }
    
    // Mengambil data profil pengguna
    public function getUserProfile(){
        $query = "SELECT id, name, email, created_at FROM " . $this->table_name . " WHERE id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row){
            $this->name = $row['name'];
            $this->email = $row['email'];
            $this->created_at = $row['created_at'];
            return true;
        }
        return false;
    }

    // Fungsi tambahan untuk memeriksa apakah email sudah ada
    public function emailExists(){
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->email);
        $stmt->execute();

        // Periksa jika ada baris yang dikembalikan
        if($stmt->rowCount() > 0){
            return true;
        }
        return false;
    }
    
}
?>