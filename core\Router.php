<?php
// Nama File: core/router.php

// Tentukan jalur dasar (base path) untuk folder proyek
// Ini penting untuk memastikan file yang di-require_once() dapat ditemukan
define('BASE_PATH', dirname(dirname(__FILE__)));

class Router {
    public static function handleApiRequest() {
        // Mendapatkan URI dari server
        $request_uri = $_SERVER['REQUEST_URI'];
        $request_method = $_SERVER['REQUEST_METHOD'];

        // Hapus path root dari URI dan trailing slash
        $api_prefix = '/api/';
        if (strpos($request_uri, $api_prefix) === 0) {
            $uri = substr($request_uri, strlen($api_prefix));
        } else {
            // Ini bukan permintaan API, kembalikan 404
            http_response_code(404);
            echo json_encode(['message' => 'API endpoint not found.']);
            return;
        }

        $uri = trim($uri, '/');
        $segments = explode('/', $uri);

        // Segmen pertama adalah resource (misalnya: 'comments', 'users')
        $resource = $segments[0] ?? null;

        // Segmen kedua adalah endpoint file (misalnya: 'read', 'create')
        $endpoint = $segments[1] ?? 'index';

        if (!$resource) {
            http_response_code(200);
            echo json_encode(['message' => 'Welcome to the API.']);
            return;
        }

        // Bangun jalur file endpoint API
        $file_path = BASE_PATH . '/api/' . $resource . '/' . $endpoint . '.php';

        // Cek apakah file endpoint ada
        if (file_exists($file_path)) {
            // Atur header
            header("Access-Control-Allow-Origin: *");
            header("Content-Type: application/json; charset=UTF-8");
            header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
            header("Access-Control-Max-Age: 3600");
            header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

            // Sertakan file
            require_once $file_path;
        } else {
            http_response_code(404);
            echo json_encode(['message' => 'Endpoint not found.']);
        }
    }
}
?>