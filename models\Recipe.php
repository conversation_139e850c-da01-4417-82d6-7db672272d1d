<?php
class Recipe{
    private $connection;
    private $table_name = "recipes";

    public $id;
    public $title;
    public $description;
    public $ingredients;
    public $steps;
    public $image;
    public $user_id;
    public $created_at;

    public function __construct($db){
        $this->connection = $db;
    }

    public function readAll(){
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY created_at DESC";
        $stmt = $this->connection->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function readSingle(){
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 1";
        $stmt = $this->connection->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row){
            $this->title =  $row['title'];
            $this->description =  $row['description'];
            $this->user_id =  $row['user_id'];
            $this->created_at =  $row['created_at'];
            return $row;

        }
        return false;

    }
    public function createRecipe(){
        // Menyiapkan resep baru
        $query = "INSERT INTO " . $this->table_name . " SET title=:title, description=:description, ingredients=:ingredients, steps=:steps, image=:image, user_id=:user_id";

        $stmt = $this->connection->prepare($query);

        // Sanitize data
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->ingredients = htmlspecialchars(strip_tags($this->ingredients));
        $this->steps = htmlspecialchars(strip_tags($this->steps));
        $this->image = htmlspecialchars(strip_tags($this->image));
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));

        // Bind parameter
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":ingredients", $this->ingredients);
        $stmt->bindParam(":steps", $this->steps);
        $stmt->bindParam(":image", $this->image);
        $stmt->bindParam(":user_id", $this->user_id);

        if($stmt->execute()){
            return true;
        }

        return false;
    }
    public function updateRecipe(){
        // Memperbarui resep yang sudah ada
        $query = "UPDATE " . $this->table_name . " SET title = :title, description = :description, ingredients = :ingredients, steps = :steps, image = :image WHERE id = :id";

        $stmt = $this->connection->prepare($query);

        // Sanitize data
        $this->id = htmlspecialchars(strip_tags($this->id));
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->ingredients = htmlspecialchars(strip_tags($this->ingredients));
        $this->steps = htmlspecialchars(strip_tags($this->steps));
        $this->image = htmlspecialchars(strip_tags($this->image));

        // Bind parameter
        $stmt->bindParam(':id', $this->id);
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':ingredients', $this->ingredients);
        $stmt->bindParam(':steps', $this->steps);
        $stmt->bindParam(':image', $this->image);

        if($stmt->execute()){
            return true;
        }

        return false;
    }
    public function deleteRecipe(){
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->connection->prepare($query);

        // Sanitize data
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameter
        $stmt->bindParam(1, $this->id);

        if ($stmt->execute()) {
            // Memeriksa apakah ada baris yang terpengaruh (artinya data berhasil dihapus)
            if ($stmt->rowCount() > 0) {
                return true;
            }
        }
        
        return false;
    }

}

?>