<?php
// Header untuk mengizinkan akses dan format JSON
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Memasukkan file database dan model user
include_once '../../core/Database.php';
include_once '../../models/User.php';

// Mendapatkan koneksi database
$database = new Database();
$db = $database->getConnection();

// Inisialisasi objek user
$user = new User($db);

// Mendapatkan data POST
$data = json_decode(file_get_contents("php://input"));

// Memeriksa jika email atau password kosong
if (empty($data->email) || empty($data->password)) {
    http_response_code(400); // Bad Request
    echo json_encode(array("message" => "Email dan password harus diisi."));
    exit();
}

// Mengatur properti objek user
$user->email = $data->email;
$user->password = $data->password;

// Mencoba login user
if ($user->loginUser()) {
    http_response_code(200); // OK
    echo json_encode(array(
        "message" => "Login berhasil.",
        "id" => $user->id,
        "name" => $user->name
    ));
} else {
    http_response_code(401); // Unauthorized
    echo json_encode(array("message" => "Login gagal. Email atau password salah."));
}
?>